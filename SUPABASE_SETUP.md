# Supabase Setup Guide

Your Next.js project is now configured to connect to Supabase! Here's what has been set up and what you need to do next.

## What's Been Configured

1. **Supabase Client**: Installed `@supabase/supabase-js` package
2. **Configuration File**: Created `lib/supabase.ts` with the Supabase client setup
3. **Environment Variables**: Created `.env.local` with placeholder values
4. **Test Page**: Updated `app/page.tsx` with a connection test component

## Next Steps

### 1. Get Your Supabase Credentials

1. Go to [supabase.com](https://supabase.com) and sign in/create an account
2. Create a new project or select an existing one
3. Go to Settings → API
4. Copy your:
   - Project URL
   - Anon/Public key

### 2. Update Environment Variables

Edit the `.env.local` file and replace the placeholder values:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
```

### 3. Test the Connection

1. Save your changes
2. Your development server should automatically reload
3. Visit your app - you should see a "Connected" status if everything is working

## File Structure

```
├── lib/
│   └── supabase.ts          # Supabase client configuration
├── app/
│   └── page.tsx             # Main page with connection test
├── .env.local               # Environment variables (not committed to git)
└── SUPABASE_SETUP.md        # This guide
```

## Usage Examples

### Basic Query
```typescript
import { supabase } from '../lib/supabase'

// Select data
const { data, error } = await supabase
  .from('your_table')
  .select('*')

// Insert data
const { data, error } = await supabase
  .from('your_table')
  .insert([{ column: 'value' }])
```

### Authentication
```typescript
// Sign up
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'password'
})

// Sign in
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password'
})
```

## Troubleshooting

- **Connection Failed**: Check your environment variables are correct
- **CORS Errors**: Make sure your domain is added to Supabase's allowed origins
- **Build Errors**: Ensure all environment variables are set in your deployment platform

## Security Notes

- Never commit `.env.local` to version control
- The anon key is safe to use in client-side code
- For server-side operations, consider using the service role key (keep it secret!)

Happy coding! 🚀
