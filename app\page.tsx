"use client";

import { useEffect, useState } from "react";
import { supabase } from "../lib/supabase";

export default function Home() {
  const [connected, setConnected] = useState<boolean | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Test Supabase connection
    const testConnection = async () => {
      try {
        const { error } = await supabase.from("_test").select("*").limit(1);
        if (error && error.code !== "PGRST116") {
          // PGRST116 is "table not found" which is expected
          throw error;
        }
        setConnected(true);
        setError(null);
      } catch (err: unknown) {
        setConnected(false);
        setError(
          err instanceof Error ? err.message : "Failed to connect to Supabase"
        );
      }
    };

    testConnection();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6 text-center">
          Supabase Connection Test
        </h1>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-gray-700">Connection Status:</span>
            <div className="flex items-center">
              {connected === null ? (
                <span className="text-yellow-600">Testing...</span>
              ) : connected ? (
                <>
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                  <span className="text-green-600">Connected</span>
                </>
              ) : (
                <>
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                  <span className="text-red-600">Disconnected</span>
                </>
              )}
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-red-800 text-sm">
                <strong>Error:</strong> {error}
              </p>
              <p className="text-red-600 text-xs mt-1">
                Make sure to update your environment variables in .env.local
              </p>
            </div>
          )}

          {connected && (
            <div className="bg-green-50 border border-green-200 rounded-md p-3">
              <p className="text-green-800 text-sm">
                ✅ Successfully connected to Supabase!
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
