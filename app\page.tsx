"use client";

import { useEffect, useState } from "react";
import { supabase } from "../lib/supabase";

export default function Home() {
  const [connected, setConnected] = useState<boolean | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Test Supabase connection
    const testConnection = async () => {
      try {
        // First check if environment variables are loaded
        if (
          !process.env.NEXT_PUBLIC_SUPABASE_URL ||
          !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
        ) {
          throw new Error(
            "Environment variables not loaded. Make sure to restart your dev server after updating .env.local"
          );
        }

        // Test connection with a simple health check
        const { error } = await supabase
          .from("_health_check")
          .select("*")
          .limit(1);

        // Both "table not found" and successful queries indicate connection is working
        if (error && !["PGRST116", "42P01"].includes(error.code || "")) {
          throw error;
        }

        setConnected(true);
        setError(null);
      } catch (err: unknown) {
        setConnected(false);
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError("Failed to connect to Supabase");
        }
        console.error("Supabase connection error:", err);
      }
    };

    testConnection();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Database Connection Notification - Bottom Left */}
      <div className="fixed bottom-4 left-4 z-50">
        <div
          className={`
          flex items-center space-x-2 px-3 py-2 rounded-lg shadow-lg backdrop-blur-sm border
          ${
            connected === null
              ? "bg-yellow-50/90 border-yellow-200 text-yellow-800"
              : connected
              ? "bg-green-50/90 border-green-200 text-green-800"
              : "bg-red-50/90 border-red-200 text-red-800"
          }
        `}
        >
          {/* Status Indicator */}
          <div
            className={`
            w-2 h-2 rounded-full
            ${
              connected === null
                ? "bg-yellow-500 animate-pulse"
                : connected
                ? "bg-green-500"
                : "bg-red-500 animate-pulse"
            }
          `}
          ></div>

          {/* Status Text */}
          <span className="text-sm font-medium">
            {connected === null
              ? "Connecting..."
              : connected
              ? "Database Connected"
              : "Database Disconnected"}
          </span>

          {/* Error Details (if any) */}
          {error && !connected && (
            <div className="ml-2">
              <button
                className="text-xs underline hover:no-underline"
                onClick={() => console.log("Error details:", error)}
                title={error}
              >
                Details
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
